import json
from typing import Dict
from utils.prompts import GptPromptPicker
from utils.logger import get_logger
from utils.gemini_client import GeminiClient
from utils.json_parser import parse_json_response
from api.time_func import timeit

logger = get_logger(__name__)
gemini_client = GeminiClient()

@timeit
async def analyze_website_presence(website_url: str) -> Dict:
    """
    Analyzes the online presence of a website.

    Args:
        website_url: The URL of the website to analyze.

    Returns:
        A dictionary containing the analysis results.
    """
    logger.info(f"Starting presence analysis for URL: {website_url}")
    prompt = GptPromptPicker.get_website_presence_prompt(website_url=website_url)

    logger.info(f"Generated prompt for {website_url} (length: {len(prompt)} characters)")
    if len(prompt) > 500:
        logger.debug(f"Prompt preview for {website_url}: {prompt[:500]}...")

    response_text = await gemini_client.make_request_async(prompt, use_grounding=True)

    if not response_text:
        logger.warning(f"No response received for URL: {website_url}")
        return {"analyzed_url": website_url, "error": "No response from API"}

    logger.info(f"Received presence analysis response for {website_url} (length: {len(response_text)} characters)")
    logger.info(f"=== PRESENCE ANALYSIS RESPONSE FOR {website_url} ===")
    logger.info(f"Raw response: {response_text}")
    logger.info(f"=== END PRESENCE ANALYSIS RESPONSE ===")

    try:
        response_json = parse_json_response(response_text, "presence analysis")
        response_json["analyzed_url"] = website_url
        logger.info(f"Successfully parsed presence analysis for {website_url}")
        logger.info(f"Parsed JSON keys: {list(response_json.keys())}")
        return response_json
    except json.JSONDecodeError as e:
        logger.error(f"Failed to decode JSON for {website_url}: {e}")
        logger.error(f"Raw response that failed to parse: {response_text}")
        return {
            "analyzed_url": website_url,
            "error": "Failed to parse response",
            "raw_response": response_text
        }