from sqlalchemy.orm import Session
from database.models import AnalysisRequest, AnalysisResult, UrlAnalysis
from database.config import get_db
from typing import List, Optional, Dict, Any
from utils.logger import get_logger
import time
from api.time_func import timeit

logger = get_logger(__name__)

class DatabaseService:
    """Service class for handling database operations."""
    
    def __init__(self, db: Session):
        self.db = db

    def save_analysis_request(self, request_data: Dict[str, Any]) -> AnalysisRequest:
        """Save incoming analysis request to database."""
        try:
            # Extract key fields from request data
            scrape_request_ref_id = request_data.get("scrapeRequestRefID")
            website_url = request_data.get("website")
            org_id = str(request_data.get("org_id", "default"))
            
            # Check if request already exists
            existing_request = self.db.query(AnalysisRequest).filter(
                AnalysisRequest.scrape_request_ref_id == scrape_request_ref_id
            ).first()
            
            if existing_request:
                logger.info(f"Request with ref_id {scrape_request_ref_id} already exists, updating...")
                existing_request.request_data = request_data
                existing_request.website_url = website_url
                existing_request.org_id = org_id
                self.db.commit()
                logger.info(f"Successfully updated request with ref_id {scrape_request_ref_id}")
                return existing_request
            
            # Create new request
            analysis_request = AnalysisRequest(
                scrape_request_ref_id=scrape_request_ref_id,
                website_url=website_url,
                org_id=org_id,
                request_data=request_data
            )
            
            self.db.add(analysis_request)
            self.db.commit()
            logger.info(f"Successfully committed new analysis request with ID: {analysis_request.id}")
            self.db.refresh(analysis_request)
            
            logger.info(f"Saved analysis request with ID: {analysis_request.id}")
            return analysis_request
            
        except Exception as e:
            logger.error(f"Error saving analysis request: {e}")
            self.db.rollback()
            raise

    def save_analysis_result(self, request_id: int, result_data: Dict[str, Any], 
                           processing_time: Optional[float] = None) -> AnalysisResult:
        """Save analysis result to database."""
        try:
            analysis_result = AnalysisResult(
                request_id=request_id,
                status=result_data.get("status", "unknown"),
                processed_urls_count=result_data.get("processed_urls", 0),
                reachable_urls_count=result_data.get("reachable_urls", 0),
                analyzed_urls_count=result_data.get("analyzed_urls", 0),
                result_data=result_data,
                processing_time_seconds=int(processing_time) if processing_time else None
            )
            
            self.db.add(analysis_result)
            self.db.commit()
            logger.info(f"Successfully committed analysis result with ID: {analysis_result.id}")
            self.db.refresh(analysis_result)
            
            logger.info(f"Saved analysis result with ID: {analysis_result.id}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Error saving analysis result: {e}")
            self.db.rollback()
            raise

    def save_url_analyses(self, request_id: int, analysis_result_id: int, 
                         url_analyses: List[Dict[str, Any]]) -> List[UrlAnalysis]:
        """Save individual URL analysis results to database."""
        try:
            saved_analyses = []
            
            for analysis_data in url_analyses:
                analyzed_url = analysis_data.get("analyzed_url")
                if not analyzed_url:
                    logger.warning("Skipping analysis data without analyzed_url")
                    continue
                
                url_analysis = UrlAnalysis.from_analysis_response(
                    request_id=request_id,
                    analysis_result_id=analysis_result_id,
                    url=analyzed_url,
                    analysis_data=analysis_data
                )
                
                self.db.add(url_analysis)
                saved_analyses.append(url_analysis)
            
            self.db.commit()
            logger.info(f"Successfully committed {len(saved_analyses)} URL analyses.")
            
            # Refresh all saved analyses
            for analysis in saved_analyses:
                self.db.refresh(analysis)
            
            logger.info(f"Saved {len(saved_analyses)} URL analyses")
            return saved_analyses
            
        except Exception as e:
            logger.error(f"Error saving URL analyses: {e}")
            self.db.rollback()
            raise

    def get_analysis_request(self, ref_id: str) -> Optional[AnalysisRequest]:
        """Get analysis request by reference ID."""
        return self.db.query(AnalysisRequest).filter(
            AnalysisRequest.scrape_request_ref_id == ref_id
        ).first()

    def get_analysis_history(self, org_id: str, limit: int = 10) -> List[AnalysisRequest]:
        """Get analysis history for an organization."""
        return self.db.query(AnalysisRequest).filter(
            AnalysisRequest.org_id == org_id
        ).order_by(AnalysisRequest.created_at.desc()).limit(limit).all()

    def get_url_analysis_summary(self, request_id: int) -> Dict[str, Any]:
        """Get summary statistics for URL analyses of a request."""
        url_analyses = self.db.query(UrlAnalysis).filter(
            UrlAnalysis.request_id == request_id
        ).all()
        
        if not url_analyses:
            return {}
        
        total_urls = len(url_analyses)
        navigation_issues = sum(1 for ua in url_analyses if ua.navigation_issues_exists)
        phishing_sites = sum(1 for ua in url_analyses if ua.phishing_site)
        malware_sites = sum(1 for ua in url_analyses if ua.malware_present)
        security_reviews = sum(1 for ua in url_analyses if ua.security_review_present)
        
        return {
            "total_urls": total_urls,
            "navigation_issues_count": navigation_issues,
            "phishing_sites_count": phishing_sites,
            "malware_sites_count": malware_sites,
            "security_reviews_count": security_reviews,
            "navigation_issues_percentage": (navigation_issues / total_urls) * 100 if total_urls > 0 else 0,
            "phishing_sites_percentage": (phishing_sites / total_urls) * 100 if total_urls > 0 else 0,
            "malware_sites_percentage": (malware_sites / total_urls) * 100 if total_urls > 0 else 0,
        }

@timeit
def save_complete_analysis(request_data: Dict[str, Any], result_data: Dict[str, Any],
                          processing_time: Optional[float] = None) -> Dict[str, Any]:
    """Save complete analysis data to database."""
    db = next(get_db())
    db_service = DatabaseService(db)
    
    try:
        # Save request
        analysis_request = db_service.save_analysis_request(request_data)
        
        # Save result
        analysis_result = db_service.save_analysis_result(
            request_id=analysis_request.id,
            result_data=result_data,
            processing_time=processing_time
        )
        
        # Save URL analyses
        url_analyses_data = result_data.get("results", [])
        if url_analyses_data:
            db_service.save_url_analyses(
                request_id=analysis_request.id,
                analysis_result_id=analysis_result.id,
                url_analyses=url_analyses_data
            )
        
        # Get summary
        summary = db_service.get_url_analysis_summary(analysis_request.id)
        
        return {
            "request_id": analysis_request.id,
            "result_id": analysis_result.id,
            "summary": summary,
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"Error saving complete analysis: {e}")
        raise
    finally:
        db.close() 