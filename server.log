INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [183809]
INFO:     Stopping reloader process [183806]
2025-08-02 00:34:07,158 - __main__ - INFO - [-] - [-] - Starting Website Health Analysis API on 0.0.0.0:8000
2025-08-02 00:34:07,158 - __main__ - INFO - [-] - [-] - Reload mode: True
INFO:     Will watch for changes in these directories: ['/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [185535] using StatReload
INFO:     Started server process [185538]
INFO:     Waiting for application startup.
2025-08-02 00:34:08,943 - database.config - INFO - [-] - [-] - Database tables created successfully
2025-08-02 00:34:08,943 - api.main - INFO - [-] - [-] - Database initialized successfully
INFO:     Application startup complete.
2025-08-02 00:34:16,079 - api.time_func - INFO - [-] - [-] - Function analyze_presence starting
2025-08-02 00:34:16,079 - api.time_func - INFO - [-] - [-] - Function analyze_website_presence starting
2025-08-02 00:34:16,079 - services.presence_analysis_service - INFO - [-] - [-] - Starting presence analysis for URL: https://www.ecomempires.in/
2025-08-02 00:34:16,079 - services.presence_analysis_service - INFO - [-] - [-] - Generated prompt for https://www.ecomempires.in/ (length: 2152 characters)
2025-08-02 00:34:16,079 - api.main - ERROR - [-] - [-] - Unexpected error during presence analysis: GeminiClient.make_request_async() got an unexpected keyword argument 'use_grounding'
Traceback (most recent call last):
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/api/main.py", line 330, in analyze_presence
    analysis_result = await analyze_website_presence(str(request.website_url))
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/api/time_func.py", line 12, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/services/presence_analysis_service.py", line 30, in analyze_website_presence
    response_text = await gemini_client.make_request_async(prompt, use_grounding=True)
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: GeminiClient.make_request_async() got an unexpected keyword argument 'use_grounding'
INFO:     127.0.0.1:54156 - "POST /analyze-website-presence HTTP/1.1" 500 Internal Server Error
2025-08-02 00:35:01,340 - api.time_func - INFO - [-] - [-] - Function analyze_health starting
2025-08-02 00:35:01,340 - api.main - INFO - [-] - [-] - Received request for website: https://www.ecomempires.in/
2025-08-02 00:35:01,340 - api.main - INFO - [-] - [-] - Start time: **********.3408875
2025-08-02 00:35:01,340 - api.main - INFO - [-] - [-] - Step 1: Processing URLs...
2025-08-02 00:35:01,340 - utils.url_processor - INFO - [-] - [-] - Processing URLs from 1 depth items
2025-08-02 00:35:01,341 - utils.url_processor - INFO - [-] - [-] - Total URLs extracted: 17
2025-08-02 00:35:01,342 - utils.url_processor - INFO - [-] - [-] - Valid URLs after filtering: 17
2025-08-02 00:35:01,342 - utils.url_processor - INFO - [-] - [-] - Unique URLs after deduplication: 17
2025-08-02 00:35:01,342 - utils.url_processor - INFO - [-] - [-] - URLs after sorting by length and taking top 100: 17
2025-08-02 00:35:01,342 - api.main - INFO - [-] - [-] - Processed 17 unique URLs from 17 total URLs
2025-08-02 00:35:01,342 - api.main - INFO - [-] - [-] - Step 2: Checking URL reachability...
2025-08-02 00:35:01,342 - api.main - ERROR - [-] - [-] - Unexpected error during analysis: object of type 'coroutine' has no len()
Traceback (most recent call last):
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/api/main.py", line 100, in analyze_health
    logger.info(f"Found {len(reachable_urls)} reachable URLs out of {len(url_dict)} processed")
                         ~~~^^^^^^^^^^^^^^^^
TypeError: object of type 'coroutine' has no len()
INFO:     127.0.0.1:54156 - "POST /analyze-website-health HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
  + Exception Group Traceback (most recent call last):
  |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_utils.py", line 76, in collapse_excgroups
  |     yield
  |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 178, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                ~~~~~~~~~~~~~~~~~~~~~~~^^
  |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 767, in __aexit__
  |     raise BaseExceptionGroup(
  |         "unhandled errors in a TaskGroup", self._exceptions
  |     )
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |         self.scope, self.receive, self.send
    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     )
    |     ^
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/applications.py", line 112, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 187, in __call__
    |     raise exc
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 165, in __call__
    |     await self.app(scope, receive, _send)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 177, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |                                    ~~~~~~~~~~~~~~~~~~^^
    |   File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     ~~~~~~~~~~~~~~^^^^^^^
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    |     raise exc
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 179, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/api/main.py", line 47, in dispatch
    |     response = await call_next(request)
    |                ^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 154, in call_next
    |     raise app_exc
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 141, in coro
    |     await self.app(scope, receive_or_disconnect, send_no_error)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    |     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/routing.py", line 715, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/routing.py", line 735, in app
    |     await route.handle(scope, receive, send)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/routing.py", line 288, in handle
    |     await self.app(scope, receive, send)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/routing.py", line 76, in app
    |     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    |     raise exc
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    |     await app(scope, receive, sender)
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/routing.py", line 73, in app
    |     response = await f(request)
    |                ^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/fastapi/routing.py", line 301, in app
    |     raw_response = await run_endpoint_function(
    |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |     ...<3 lines>...
    |     )
    |     ^
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    |     return await dependant.call(**values)
    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/api/time_func.py", line 12, in wrapper
    |     result = await func(*args, **kwargs)
    |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    |   File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/api/main.py", line 179, in analyze_health
    |     "reachable_urls": len(reachable_urls) if 'reachable_urls' in locals() else 0,
    |                       ~~~^^^^^^^^^^^^^^^^
    | TypeError: object of type 'coroutine' has no len()
    +------------------------------------

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        self.scope, self.receive, self.send
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 177, in __call__
    with recv_stream, send_stream, collapse_excgroups():
                                   ~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/.linuxbrew/opt/python@3.13/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    ~~~~~~~~~~~~~~^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_utils.py", line 82, in collapse_excgroups
    raise exc
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 179, in __call__
    response = await self.dispatch_func(request, call_next)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/api/main.py", line 47, in dispatch
    response = await call_next(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 154, in call_next
    raise app_exc
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/routing.py", line 735, in app
    await route.handle(scope, receive, send)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/venv/lib/python3.13/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/api/time_func.py", line 12, in wrapper
    result = await func(*args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Desktop/mcc-only/WebReview_DS_API_24Jun/api/main.py", line 179, in analyze_health
    "reachable_urls": len(reachable_urls) if 'reachable_urls' in locals() else 0,
                      ~~~^^^^^^^^^^^^^^^^
TypeError: object of type 'coroutine' has no len()
