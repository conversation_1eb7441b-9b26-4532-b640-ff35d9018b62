from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, Foreign<PERSON>ey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.config import Base
import json

class AnalysisRequest(Base):
    """Model for storing incoming analysis requests."""
    __tablename__ = "analysis_requests"

    id = Column(Integer, primary_key=True, index=True)
    scrape_request_ref_id = Column(String(255), unique=True, index=True, nullable=False)
    website_url = Column(String(500), nullable=False)
    org_id = Column(String(100), nullable=False)
    request_data = Column(JSON, nullable=False)  # Store the full request payload
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    # Relationships
    analysis_results = relationship("AnalysisResult", back_populates="request")
    url_analyses = relationship("UrlAnalysis", back_populates="request")

    def __repr__(self):
        return f"<AnalysisRequest(id={self.id}, ref_id='{self.scrape_request_ref_id}', website='{self.website_url}')>"

class AnalysisResult(Base):
    """Model for storing overall analysis results."""
    __tablename__ = "analysis_results"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(Integer, ForeignKey("analysis_requests.id"), nullable=False)
    status = Column(String(50), nullable=False)  # success, warning, error
    processed_urls_count = Column(Integer, default=0)
    reachable_urls_count = Column(Integer, default=0)
    analyzed_urls_count = Column(Integer, default=0)
    result_data = Column(JSON, nullable=False)  # Store the full result payload
    error_message = Column(Text, nullable=True)
    processing_time_seconds = Column(Integer, nullable=True)
    created_at = Column(DateTime, server_default=func.now())

    # Relationships
    request = relationship("AnalysisRequest", back_populates="analysis_results")
    url_analyses = relationship("UrlAnalysis", back_populates="analysis_result")

    def __repr__(self):
        return f"<AnalysisResult(id={self.id}, request_id={self.request_id}, status='{self.status}')>"

class UrlAnalysis(Base):
    """Model for storing individual URL analysis results."""
    __tablename__ = "url_analyses"

    id = Column(Integer, primary_key=True, index=True)
    request_id = Column(Integer, ForeignKey("analysis_requests.id"), nullable=False)
    analysis_result_id = Column(Integer, ForeignKey("analysis_results.id"), nullable=False)
    analyzed_url = Column(String(500), nullable=False)
    
    # Navigation issues
    navigation_issues_exists = Column(Boolean, nullable=False)
    navigation_issue_types = Column(JSON, nullable=True)  # List of issue types
    navigation_issues_areas = Column(JSON, nullable=True)  # List of affected areas
    redirection_same_page = Column(Boolean, nullable=False)
    problematic_links = Column(JSON, nullable=True)  # List of problematic links
    
    # Security assessment
    phishing_site = Column(Boolean, nullable=False)
    phishing_reason = Column(Text, nullable=True)
    malware_present = Column(Boolean, nullable=False)
    malware_reason = Column(Text, nullable=True)
    security_review_present = Column(Boolean, nullable=False)
    security_review = Column(Text, nullable=True)
    security_review_sources = Column(JSON, nullable=True)  # List of sources
    
    # Raw analysis data
    raw_analysis_data = Column(JSON, nullable=False)  # Store the complete analysis response
    analysis_timestamp = Column(DateTime, server_default=func.now())

    # Relationships
    request = relationship("AnalysisRequest", back_populates="url_analyses")
    analysis_result = relationship("AnalysisResult", back_populates="url_analyses")

    def __repr__(self):
        return f"<UrlAnalysis(id={self.id}, url='{self.analyzed_url}', phishing={self.phishing_site}, malware={self.malware_present})>"

    @classmethod
    def from_analysis_response(cls, request_id: int, analysis_result_id: int, url: str, analysis_data: dict):
        """Create UrlAnalysis instance from analysis response data."""
        return cls(
            request_id=request_id,
            analysis_result_id=analysis_result_id,
            analyzed_url=url,
            navigation_issues_exists=analysis_data.get("navigation_issues_exists", "no").lower() == "yes",
            navigation_issue_types=analysis_data.get("navigation_issue_type", []),
            navigation_issues_areas=analysis_data.get("navigation_issues_area", []),
            redirection_same_page=analysis_data.get("redirection_same_page", "no").lower() == "yes",
            problematic_links=analysis_data.get("links", []),
            phishing_site=analysis_data.get("phishing_site", "no").lower() == "yes",
            phishing_reason=analysis_data.get("phishing_reason", ""),
            malware_present=analysis_data.get("malware_present", "no").lower() == "yes",
            malware_reason=analysis_data.get("malware_reason", ""),
            security_review_present=analysis_data.get("security_review_present", "no").lower() == "yes",
            security_review=analysis_data.get("security_review", ""),
            security_review_sources=analysis_data.get("security_review_source", []),
            raw_analysis_data=analysis_data
        ) 