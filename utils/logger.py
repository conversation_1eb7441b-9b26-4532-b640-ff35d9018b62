import logging
import sys
import contextvars

# Context variables for orgId and refId
org_id_var = contextvars.ContextVar('org_id', default='-')
ref_id_var = contextvars.ContextVar('ref_id', default='-')

class ContextFilter(logging.Filter):
    """
    A filter to add contextual information like orgId and refId from contextvars to log records.
    """
    def filter(self, record):
        record.orgId = org_id_var.get()
        record.refId = ref_id_var.get()
        return True

def get_logger(name: str):
    """
    Creates and configures a logger.

    Args:
        name: The name of the logger.

    Returns:
        A configured logger instance.
    """
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # Create a handler to print log messages to the console
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.INFO)

    # Create a formatter and set it for the handler
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(orgId)s] - [%(refId)s] - %(message)s'
    )
    handler.setFormatter(formatter)

    # Add the handler and filter to the logger
    if not logger.handlers:
        logger.addHandler(handler)
        logger.addFilter(ContextFilter())

    return logger