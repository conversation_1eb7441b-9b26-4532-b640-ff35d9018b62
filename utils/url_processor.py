from typing import List, Dict
from api.request import UrlDepthItem
from utils.logger import get_logger
import re

logger = get_logger(__name__)

def is_valid_url(url: str) -> bool:
    """
    Validates if a URL is properly formatted.

    Args:
        url: The URL string to validate

    Returns:
        bool: True if URL is valid, False otherwise
    """
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    return url_pattern.match(url) is not None

def process_urls(parsed_urls: List[UrlDepthItem]) -> Dict[int, str]:
    """
    Processes a list of URLs by deduplicating, sorting, and selecting the top 100.

    Args:
        parsed_urls: A list of UrlDepthItem objects from the request.

    Returns:
        A dictionary of the top 100 URLs, with integer keys.
    """
    logger.info(f"Processing URLs from {len(parsed_urls)} depth items")

    # 1. Get all extracted URLs
    all_urls = []
    for item in parsed_urls:
        logger.debug(f"Processing depth {item.url_depth} with {len(item.urls)} URLs")
        all_urls.extend(item.urls)

    logger.info(f"Total URLs extracted: {len(all_urls)}")

    # 2. Filter out invalid URLs and dedupe
    valid_urls = [url for url in all_urls if url and is_valid_url(url.strip())]
    logger.info(f"Valid URLs after filtering: {len(valid_urls)}")

    unique_urls = sorted(list(set(valid_urls)))
    logger.info(f"Unique URLs after deduplication: {len(unique_urls)}")

    # 3. Sort the URLs in ascending order of length and take top 100
    sorted_urls = sorted(unique_urls, key=len)[:100]
    logger.info(f"URLs after sorting by length and taking top 100: {len(sorted_urls)}")

    # 4. Convert it into a dictionary
    url_dict = {i: url for i, url in enumerate(sorted_urls)}

    logger.debug(f"Final URL dictionary: {url_dict}")
    return url_dict