import json


class GptPromptPicker:
    @staticmethod
    def get_website_reachability_prompt(url_batch: dict):
        """
        Generates a prompt to check the reachability of a batch of URLs.

        Args:
            url_batch: A dictionary of URLs to check, with integer keys.

        Returns:
            A string containing the prompt for the language model.
        """
        # Create a formatted list of URLs for the prompt
        url_list = "\n".join([f"{key}: {url}" for key, url in url_batch.items()])

        system = f"""
        *Context*
        You are an expert in website analysis and network diagnostics. You have been provided with a dictionary of URLs and your task is to determine which of them are reachable and can be read by <PERSON> with URL context capabilities.

        *Input Data*
        URLs to be checked (total: {len(url_batch)} URLs):
        {url_list}

        *Task*
        Your job is to analyze each URL listed above and determine if it is reachable and its content can be accessed. A URL is considered "reachable" if:
        1. It returns a successful HTTP status code (e.g., 200 OK)
        2. Its content is accessible and readable
        3. The page loads without critical errors
        4. The content can be analyzed by AI tools

        *Guidelines*
        1. Use the URL context tool to attempt to access ONLY the URLs listed in the Input Data section above
        2. Check for successful page loading and content accessibility for each URL
        3. Verify that the content is not blocked, password-protected, or behind a paywall
        4. Consider a URL unreachable if it results in 404 errors, server errors, timeouts, or access restrictions
        5. Do NOT attempt to access any URLs not explicitly listed in the Input Data section

        *Output Structure*
        Return ONLY a valid JSON object with a single key "reachable_urls" containing a list of integer keys corresponding to successfully reachable URLs from the Input Data section.

        Example:
        {{
            "reachable_urls": [0, 2, 5]
        }}
        """
        return system

    @staticmethod
    def get_website_health_analysis_prompt(website_url: str):

        prompt = f"""
                You are an expert in website security analysis and usability assessment. Using the URL context tool, please analyze the following website: {website_url}

                IMPORTANT: Use the URL context tool to access and analyze the actual website content. Additionally, use grounding/search capabilities to research the website's reputation, security reports, and user reviews.

                Access the website content and perform a comprehensive security and usability analysis. Check for these specific areas:
                *** Page Navigation Issues ***
                1. Page Navigation Issues: Analyze the page's content and link structure to identify any potential navigation problems or a confusing layout.
                2. If there are some inactive buttons or dropdowns etc. which dont work at all and clickable, which are in general links on other websites, like privacy, about us, terms and conditions etc. 
                3. Redirection to same page" --> If there are some links which gets redirected back to the same page. For this analyze the Link's Destination (href): For each link found, analyze its href attribute to see where it points, specifically look for links that:
                    a. Point to the exact same URL as the page I'm currently on.
                    b. Use a hash symbol (#) as the destination (e.g., href="#"). In web development, this is a standard way to create a link that goes to the top of the current page, effectively reloading or refocusing it.
                    c. Have an empty href attribute (href=""), which also causes a reload of the current page.
                *** Website Security Issues***
                1. Phishing Indicators: Assess whether the site has the characteristics of a phishing attempt. Look for suspicious elements in the content, check the URL format, and search for its reputation using grounding/search capabilities.
                2. Malware Risk: Investigate if the URL is flagged on any known security blacklists or has a reputation for distributing malware. Use grounding to search for security reports.
                3. Security Threat Listings: Check if this URL or site is listed anywhere as a security threat using search capabilities.
                4. User Reviews and Scam Reports: Search for reviews where users indicate the site is unsafe, has security concerns, or customers have been scammed.

                *** Guidelines ***
                Page navigation issues can be following for example.
                1. Broken Links  --->  Clicking a link leads to a 404 or non-functional page ---> “Contact Us” → 404 Not Found                                              
                2. JavaScript Errors ---> JS-based navigation buttons don’t respond or crash ---> Clicking a dropdown fails silently                                        
                3. Login Walls --->Clicking a link redirects to login or signup ---> “Pricing” → requires login                                                
                4. Infinite Redirect Loops ---> Page keeps redirecting between URLs ---> `/home` → `/main` → `/home`...                                            
                5. Disabled Links ---> Links or buttons appear clickable but do nothing ---> “Terms & Conditions” button does nothing                                  
                6. Non-standard Navigation ---> Website uses dynamic pop-ups/modals instead of clean page loads ---> Navigation inside single-page apps (SPAs) fails without proper wait logic |

                Output should strictly be in below given format as a json with 9 keys.
                {{"navigation_issues_exists": a string, yes/no, 
                "navigation_issue_type": a list [], one amongst mentioned in "Page navigation issues"
                "navigation_issues_area": a list [], a list of buttons, dropdowns, etc. whch are inactive and/or are dummy. 
                "redirection_same_page": a string, yes/no, Yes if there are some links which redirects back to the same page
                "links" : a list [], a list of links which redirect to the same page again  in case they exists.
                "phishing_site" : a string, yes/no, if its a phishing website
                "phishing_reason": a string, short reason if it was classified at phishing site, in 10-15 words.
                "malware_present: a string, yes/no, yes if there is a malware present in it
                "malware_reason":a string, short reason if it was found that a malware was present, in 10-15 words.
                "security_review_present" : a string, yes/no, yes if there a exists a review online where this site has been mentioned for a security related issue
                "security_review": a string, if a security related issue was found, a short description of the issue, in 10-15 words.
                "security_review_source": a list [], a list of platforms where the review has been mentioned.
                }}
        """

        return prompt

  

    def get_website_presence_prompt(website_url: str):

        prompt = f"""
        You an expert business analyst who analyses a website's presence on the internet. 
        You will be a given a website and you have analyse if this website is ranked decently on the search, how much is the traffic on this site, how are the reviews of this site etc.

        *** Website *** ---> 

        *** Tasks Starts ***
        Given a website you have to 
        1. First check if the wesbite is active or not.
        2. Domain should be up and it should not be that website is Non-Operational for example home itself, 404 not found, or containing clear indicators like " domain up for sale", "coming soon," "under construction," "website expired," or "this account has been suspended", "blank pages of domain provider", or other similar information.
        3. Once you have established that website is working fine you will have to check how often does it appear on search.
        4. You will have to look for the reviews of this website on all over the internet and check if it good or bad.
        5. You will have to check if it has a valid social media URL, you need not visit the social media.
        6. You will have to check if the website has security related issues or not.
        7. You will have to check if people visit that site or not.
        *** Tasks Ends ***

        The output shoud be in the following format, it should be a json with only these keys and nothing apart from this.
        {{"website_functional" : a string, yes/no
        "search_rank": a string, one amongst, "high", "medium", "low".
        "reviews" : a list of dictionaries, [{{"platform_name": a string, "rating":""}}, {{"platform_name": a string, "rating":""}}...]
        "traffic": a string, one amongst, "high", "medium", "low".
        "social_media_presence": a list of dictionaries, [{{"platform":a string like youtube, instagram, etc, "followers":an integer}},.....]
        }}

        Make sure that if any informtion is not available, just put is as blank string or empty list. Do not write any justificaiton of why the information is not present or why it was not able to extract.
        JSON structure should not break.
        """

        return prompt




