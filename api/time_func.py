import functools
from utils.logger import get_logger
import time

logger = get_logger(__name__) 

def timeit(func):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        logger.info(f"Function {func.__name__} starting")
        result = await func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"Function {func.__name__} finished in {end_time - start_time:.2f} seconds")
        return result
    return wrapper